from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, HttpUrl
from typing import List, Optional, Dict, Any
import sys
import os

# Add the parent directory to the path for absolute imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.seo_team import seo_team
from agents.seo_agent import seo_agent
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/seo", tags=["SEO Analysis"])


# Basic SEO Analysis Models
class WebsiteAnalysisRequest(BaseModel):
    url: HttpUrl
    analysis_type: Optional[str] = "comprehensive"
    include_competitors: Optional[bool] = False
    competitor_urls: Optional[List[HttpUrl]] = []


class CompetitorComparisonRequest(BaseModel):
    target_url: HttpUrl
    competitor_urls: List[HttpUrl]


class SEOReportRequest(BaseModel):
    url: HttpUrl
    analysis_data: Dict[str, Any]


# Enhanced SEO Analysis Models
class ContentAnalysisRequest(BaseModel):
    url: HttpUrl
    content_type: Optional[str] = "page"


class KeywordResearchRequest(BaseModel):
    topic: str
    location: Optional[str] = "global"


class ContentBriefRequest(BaseModel):
    topic: str
    target_keywords: List[str]


class CompetitorKeywordRequest(BaseModel):
    competitor_urls: List[HttpUrl]


class ComprehensiveAnalysisRequest(BaseModel):
    url: HttpUrl
    include_competitors: Optional[bool] = False
    competitor_urls: Optional[List[HttpUrl]] = []


class AutomatedCrawlRequest(BaseModel):
    url: HttpUrl
    max_pages: Optional[int] = 50


class SiteMonitoringRequest(BaseModel):
    url: HttpUrl
    previous_crawl_data: Dict[str, Any]


@router.post("/analyze")
async def analyze_website(request: WebsiteAnalysisRequest, background_tasks: BackgroundTasks):
    """Analyze a website for SEO performance"""

    try:
        # Perform SEO analysis
        analysis_result = await seo_agent.analyze_website(
            str(request.url),
            request.analysis_type or "comprehensive"
        )

        if not analysis_result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Analysis failed: {analysis_result.get('error', 'Unknown error')}"
            )

        # If competitor analysis is requested
        if request.include_competitors and request.competitor_urls:
            competitor_result = await seo_agent.compare_competitors(
                str(request.url),
                [str(url) for url in request.competitor_urls]
            )

            if competitor_result["success"]:
                analysis_result["competitor_comparison"] = competitor_result["comparison_results"]

        return {
            "status": "success",
            "data": analysis_result
        }

    except Exception as e:
        logger.error(f"SEO analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.post("/compare-competitors")
async def compare_competitors(request: CompetitorComparisonRequest):
    """Compare target website with competitors"""

    try:
        if len(request.competitor_urls) < 1:
            raise HTTPException(
                status_code=400,
                detail="At least one competitor URL is required"
            )

        if len(request.competitor_urls) > 5:
            raise HTTPException(
                status_code=400,
                detail="Maximum 5 competitor URLs allowed"
            )

        comparison_result = await seo_agent.compare_competitors(
            str(request.target_url),
            [str(url) for url in request.competitor_urls]
        )

        if not comparison_result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Comparison failed: {comparison_result.get('error', 'Unknown error')}"
            )

        return {
            "status": "success",
            "data": comparison_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Competitor comparison error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")


@router.post("/generate-report")
async def generate_seo_report(request: SEOReportRequest):
    """Generate a comprehensive SEO report"""

    try:
        report_result = await seo_agent.generate_seo_report(
            str(request.url),
            request.analysis_data
        )

        if not report_result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Report generation failed: {report_result.get('error', 'Unknown error')}"
            )

        return {
            "status": "success",
            "data": report_result
        }

    except Exception as e:
        logger.error(f"Report generation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Report generation failed: {str(e)}")


@router.get("/health")
async def seo_agent_health():
    """Check SEO agent health and configuration"""

    try:
        # Check if agent is initialized
        agent_status = "initialized" if seo_agent.agent else "not_initialized"

        # Check available tools
        tools_status = []
        if hasattr(seo_agent, 'agent') and seo_agent.agent:
            # Get tools from agent if available
            tools_status = ["SEO Analysis Tools"]

        # Check knowledge base
        knowledge_status = "configured" if hasattr(seo_agent, 'knowledge_base') and seo_agent.knowledge_base else "not_configured"

        return {
            "status": "healthy",
            "agent": agent_status,
            "tools": tools_status,
            "knowledge_base": knowledge_status,
            "firecrawl_configured": False  # Will be updated when we fix the agents
        }

    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }


# Enhanced SEO Analysis Endpoints

@router.post("/content-analysis")
async def analyze_content(request: ContentAnalysisRequest):
    """Analyze content for SEO and quality metrics"""
    try:
        # Use the team for enhanced analysis if available
        if hasattr(seo_team, 'team') and seo_team.team:
            analysis_result = await seo_team.analyze_content(
                str(request.url),
                request.content_type or "page"
            )
        else:
            # Fallback to basic agent
            analysis_result = await seo_agent.analyze_website(
                str(request.url),
                "content_analysis"
            )

        if not analysis_result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Content analysis failed: {analysis_result.get('error', 'Unknown error')}"
            )

        return {
            "status": "success",
            "data": analysis_result
        }

    except Exception as e:
        logger.error(f"Content analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Content analysis failed: {str(e)}")


@router.post("/keyword-research")
async def research_keywords(request: KeywordResearchRequest):
    """Research keywords for a given topic"""
    try:
        # Use the team for enhanced analysis if available
        if hasattr(seo_team, 'team') and seo_team.team:
            research_result = await seo_team.research_keywords(
                request.topic,
                request.location or "global"
            )
        else:
            # Fallback to basic agent with keyword focus
            research_result = await seo_agent.analyze_website(
                f"https://www.google.com/search?q={request.topic}",
                "keyword_research"
            )

        if not research_result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Keyword research failed: {research_result.get('error', 'Unknown error')}"
            )

        return {
            "status": "success",
            "data": research_result
        }

    except Exception as e:
        logger.error(f"Keyword research error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Keyword research failed: {str(e)}")


@router.post("/comprehensive-analysis")
async def comprehensive_seo_analysis(request: ComprehensiveAnalysisRequest):
    """Perform comprehensive SEO analysis using the entire team"""
    try:
        competitor_urls = [str(url) for url in (request.competitor_urls or [])]

        # Use the team for comprehensive analysis if available
        if hasattr(seo_team, 'team') and seo_team.team:
            analysis_result = await seo_team.comprehensive_analysis(
                str(request.url),
                request.include_competitors or False,
                competitor_urls
            )
        else:
            # Fallback to enhanced basic analysis
            analysis_result = await seo_agent.analyze_website(
                str(request.url),
                "comprehensive"
            )

            # Add competitor analysis if requested
            if request.include_competitors and competitor_urls:
                competitor_result = await seo_agent.compare_competitors(
                    str(request.url),
                    competitor_urls
                )
                if competitor_result.get("success", False):
                    analysis_result["competitor_comparison"] = competitor_result.get("comparison_results", {})

        if not analysis_result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Comprehensive analysis failed: {analysis_result.get('error', 'Unknown error')}"
            )

        return {
            "status": "success",
            "data": analysis_result
        }

    except Exception as e:
        logger.error(f"Comprehensive analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Comprehensive analysis failed: {str(e)}")


@router.get("/team-health")
async def team_health_check():
    """Check the health of all SEO analysis agents and team"""
    try:
        # Check individual agents
        agents_status = {
            "seo_agent": "initialized" if hasattr(seo_agent, 'agent') and seo_agent.agent else "not_initialized",
        }

        # Check team
        team_status = "initialized" if hasattr(seo_team, 'team') and seo_team.team else "not_initialized"

        # Check tools
        tools_status = ["Basic SEO Tools"]

        return {
            "status": "healthy",
            "team": team_status,
            "agents": agents_status,
            "tools": tools_status,
            "agno_configured": True
        }

    except Exception as e:
        logger.error(f"Team health check error: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }