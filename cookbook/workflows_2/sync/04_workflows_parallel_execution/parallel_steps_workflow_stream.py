from agno.agent import Agent
from agno.tools.googlesearch import GoogleSearchTools
from agno.tools.hackernews import HackerNewsTools
from agno.workflow.v2 import Step, Workflow
from agno.workflow.v2.parallel import Parallel

# Create agents
researcher = Agent(name="Researcher", tools=[HackerNewsTools(), GoogleSearchTools()])
writer = Agent(name="Writer")
reviewer = Agent(name="Reviewer")

# Create individual steps
research_hn_step = Step(name="Research HackerNews", agent=researcher)
research_web_step = Step(name="Research Web", agent=researcher)
write_step = Step(name="Write Article", agent=writer)
review_step = Step(name="Review Article", agent=reviewer)

# Create workflow with direct execution
workflow = Workflow(
    name="Content Creation Pipeline",
    steps=[
        Parallel(research_hn_step, research_web_step, name="Research Phase"),
        write_step,
        review_step,
    ],
)

workflow.print_response(
    "Write about the latest AI developments",
    stream=True,
    stream_intermediate_steps=True,
)
