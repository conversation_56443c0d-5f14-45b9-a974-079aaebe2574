"""
TrendScannerAgent: Monitors trending topics and news for content inspiration.

This agent uses various sources to identify trending topics relevant to the configured keywords.
It produces structured trend reports that can be consumed by the ContentCreatorAgent.
"""

import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.hackernews import HackerNewsTools
from agno.tools.file import FileTools

from ..config.settings import SocialMediaSettings, TrendSource


class TrendData:
    """Data structure for trend information."""
    
    def __init__(self, title: str, url: str, summary: str, source: str, 
                 keywords: List[str], popularity_score: float = 0.0):
        self.title = title
        self.url = url
        self.summary = summary
        self.source = source
        self.keywords = keywords
        self.popularity_score = popularity_score
        self.discovered_at = datetime.now().isoformat()
        self.id = self._generate_id()
    
    def _generate_id(self) -> str:
        """Generate a unique ID for this trend."""
        content = f"{self.title}{self.url}{self.source}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "title": self.title,
            "url": self.url,
            "summary": self.summary,
            "source": self.source,
            "keywords": self.keywords,
            "popularity_score": self.popularity_score,
            "discovered_at": self.discovered_at
        }


class TrendScannerAgent(Agent):
    """
    Agent responsible for scanning trending topics across multiple sources.
    
    This agent monitors news, social media, and other sources to identify
    trending topics relevant to the configured keywords. It produces structured
    trend reports for consumption by other agents in the pipeline.
    """
    
    def __init__(self, settings: Optional[SocialMediaSettings] = None):
        """
        Initialize the TrendScannerAgent.
        
        Args:
            settings: Configuration settings for the agent
        """
        self.settings = settings or SocialMediaSettings()
        self.logger = logging.getLogger(__name__)
        
        # Initialize tools
        self.search_tools = DuckDuckGoTools()
        self.hackernews_tools = HackerNewsTools()
        self.file_tools = FileTools(base_dir=self.settings.trend_reports_dir)
        
        # Ensure output directory exists
        Path(self.settings.trend_reports_dir).mkdir(parents=True, exist_ok=True)
        
        super().__init__(
            name="TrendScanner",
            role="Monitor trending topics and news relevant to our brand",
            model=OpenAIChat(id=self.settings.content_generation_model),
            tools=[self.search_tools, self.hackernews_tools, self.file_tools],
            instructions=[
                "You are a trend scanning agent responsible for identifying trending topics.",
                f"Focus on topics related to: {', '.join(self.settings.keywords)}",
                "Analyze the relevance and popularity of each topic you find.",
                "Provide concise summaries that highlight why each trend is interesting.",
                "Always include the source URL for verification.",
                "Rate trends on popularity from 0.0 to 1.0 based on engagement and coverage."
            ],
            add_history_to_messages=True,
            add_datetime_to_instructions=True,
            debug_mode=False
        )
    
    def scan_news_trends(self, keywords: List[str], max_results: int = 5) -> List[TrendData]:
        """
        Scan news sources for trending topics.
        
        Args:
            keywords: List of keywords to search for
            max_results: Maximum number of trends to return per keyword
            
        Returns:
            List of TrendData objects
        """
        trends = []
        
        for keyword in keywords:
            try:
                self.logger.info(f"Scanning news trends for keyword: {keyword}")
                
                # Search for recent news
                query = f"{keyword} news"
                search_results = self.search_tools.search(query, max_results=max_results)
                
                if search_results and isinstance(search_results, list):
                    for result in search_results:
                        if isinstance(result, dict) and 'title' in result:
                            trend = TrendData(
                                title=result.get('title', ''),
                                url=result.get('url', ''),
                                summary=result.get('body', '')[:200] + '...',
                                source='news',
                                keywords=[keyword],
                                popularity_score=0.5  # Default score, could be enhanced
                            )
                            trends.append(trend)
                            
            except Exception as e:
                self.logger.error(f"Error scanning news for keyword {keyword}: {e}")
        
        return trends
    
    def scan_hackernews_trends(self, keywords: List[str]) -> List[TrendData]:
        """
        Scan Hacker News for trending topics.
        
        Args:
            keywords: List of keywords to search for
            
        Returns:
            List of TrendData objects
        """
        trends = []
        
        try:
            self.logger.info("Scanning Hacker News for trends")
            
            # Get top stories from Hacker News
            top_stories = self.hackernews_tools.get_top_stories(limit=20)
            
            if top_stories:
                for story in top_stories:
                    if isinstance(story, dict):
                        title = story.get('title', '').lower()
                        
                        # Check if any keyword matches the title
                        matching_keywords = [kw for kw in keywords if kw.lower() in title]
                        
                        if matching_keywords:
                            trend = TrendData(
                                title=story.get('title', ''),
                                url=story.get('url', ''),
                                summary=f"Trending on Hacker News with {story.get('score', 0)} points",
                                source='hackernews',
                                keywords=matching_keywords,
                                popularity_score=min(story.get('score', 0) / 500.0, 1.0)
                            )
                            trends.append(trend)
                            
        except Exception as e:
            self.logger.error(f"Error scanning Hacker News: {e}")
        
        return trends
    
    def scan_reddit_trends(self, keywords: List[str]) -> List[TrendData]:
        """
        Scan Reddit for trending topics using search.
        
        Args:
            keywords: List of keywords to search for
            
        Returns:
            List of TrendData objects
        """
        trends = []
        
        for keyword in keywords:
            try:
                self.logger.info(f"Scanning Reddit trends for keyword: {keyword}")
                
                # Search Reddit via DuckDuckGo
                query = f"site:reddit.com {keyword}"
                search_results = self.search_tools.search(query, max_results=3)
                
                if search_results and isinstance(search_results, list):
                    for result in search_results:
                        if isinstance(result, dict) and 'reddit.com' in result.get('url', ''):
                            trend = TrendData(
                                title=result.get('title', ''),
                                url=result.get('url', ''),
                                summary=result.get('body', '')[:200] + '...',
                                source='reddit',
                                keywords=[keyword],
                                popularity_score=0.4  # Reddit content gets default score
                            )
                            trends.append(trend)
                            
            except Exception as e:
                self.logger.error(f"Error scanning Reddit for keyword {keyword}: {e}")
        
        return trends
    
    def analyze_trend_relevance(self, trend: TrendData) -> float:
        """
        Analyze how relevant a trend is to our brand and audience.
        
        Args:
            trend: Trend data to analyze
            
        Returns:
            Relevance score from 0.0 to 1.0
        """
        try:
            analysis_prompt = f"""
            Analyze the relevance of this trend to a brand focused on {', '.join(self.settings.keywords)}:
            
            Title: {trend.title}
            Summary: {trend.summary}
            Keywords: {', '.join(trend.keywords)}
            
            Rate the relevance from 0.0 to 1.0 where:
            - 1.0 = Highly relevant, directly related to our focus areas
            - 0.5 = Somewhat relevant, tangentially related
            - 0.0 = Not relevant at all
            
            Respond with just the score (e.g., 0.7).
            """
            
            response = self.run(analysis_prompt)
            
            # Try to extract a numeric score from the response
            import re
            score_match = re.search(r'(\d+\.?\d*)', str(response.content))
            if score_match:
                score = float(score_match.group(1))
                return min(max(score, 0.0), 1.0)  # Clamp between 0 and 1
                
        except Exception as e:
            self.logger.error(f"Error analyzing trend relevance: {e}")
        
        return 0.5  # Default relevance score
    
    def get_trends(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Get current trends, optionally forcing a fresh scan.
        
        Args:
            force_refresh: If True, performs a new scan regardless of cache
            
        Returns:
            List of trend dictionaries
        """
        # Check if we have recent trends cached
        recent_report_path = self._get_latest_report_path()
        
        if not force_refresh and recent_report_path and recent_report_path.exists():
            # Check if the report is recent enough
            report_age = datetime.now() - datetime.fromtimestamp(recent_report_path.stat().st_mtime)
            if report_age.total_seconds() < self.settings.trend_scan_interval_hours * 3600:
                self.logger.info("Using cached trend report")
                with open(recent_report_path, 'r') as f:
                    cached_data = json.load(f)
                return cached_data.get('trends', [])
        
        # Perform fresh scan
        self.logger.info("Performing fresh trend scan")
        return self.daily_report()
    
    def daily_report(self) -> List[Dict[str, Any]]:
        """
        Generate a comprehensive daily trend report.
        
        Returns:
            List of trend dictionaries
        """
        self.logger.info("Generating daily trend report")
        all_trends = []
        
        # Scan different sources based on configuration
        if TrendSource.NEWS in self.settings.trend_sources:
            news_trends = self.scan_news_trends(self.settings.keywords, max_results=3)
            all_trends.extend(news_trends)
        
        if TrendSource.HACKERNEWS in self.settings.trend_sources:
            hn_trends = self.scan_hackernews_trends(self.settings.keywords)
            all_trends.extend(hn_trends)
        
        if TrendSource.REDDIT in self.settings.trend_sources:
            reddit_trends = self.scan_reddit_trends(self.settings.keywords)
            all_trends.extend(reddit_trends)
        
        # Analyze relevance for each trend
        for trend in all_trends:
            relevance = self.analyze_trend_relevance(trend)
            trend.popularity_score = (trend.popularity_score + relevance) / 2
        
        # Filter by popularity threshold and limit results
        filtered_trends = [
            trend for trend in all_trends 
            if trend.popularity_score >= self.settings.trend_popularity_threshold
        ]
        
        # Sort by popularity and limit results
        filtered_trends.sort(key=lambda x: x.popularity_score, reverse=True)
        final_trends = filtered_trends[:self.settings.max_trends_per_scan]
        
        # Save report
        report_data = {
            "generated_at": datetime.now().isoformat(),
            "settings": {
                "keywords": self.settings.keywords,
                "sources": [source.value for source in self.settings.trend_sources],
                "threshold": self.settings.trend_popularity_threshold
            },
            "trends": [trend.to_dict() for trend in final_trends],
            "stats": {
                "total_discovered": len(all_trends),
                "after_filtering": len(filtered_trends),
                "final_count": len(final_trends)
            }
        }
        
        self._save_report(report_data)
        
        self.logger.info(f"Generated report with {len(final_trends)} trends")
        return report_data["trends"]
    
    def _get_latest_report_path(self) -> Optional[Path]:
        """Get the path to the most recent trend report."""
        reports_dir = Path(self.settings.trend_reports_dir)
        if not reports_dir.exists():
            return None
        
        report_files = list(reports_dir.glob("trend_report_*.json"))
        if not report_files:
            return None
        
        # Return the most recently modified file
        return max(report_files, key=lambda p: p.stat().st_mtime)
    
    def _save_report(self, report_data: Dict[str, Any]):
        """Save trend report to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"trend_report_{timestamp}.json"
        filepath = Path(self.settings.trend_reports_dir) / filename
        
        try:
            with open(filepath, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            # Also save as 'latest' for easy access
            latest_path = Path(self.settings.trend_reports_dir) / "latest_trend_report.json"
            with open(latest_path, 'w') as f:
                json.dump(report_data, f, indent=2)
                
            self.logger.info(f"Trend report saved to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving trend report: {e}")
