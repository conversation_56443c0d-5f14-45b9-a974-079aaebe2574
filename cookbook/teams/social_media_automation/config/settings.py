"""
Configuration settings for the Social Media Automation team.
"""

import os
from typing import List, Dict, Any, Optional
from enum import Enum
from pydantic import BaseSettings, Field, validator
from datetime import datetime, time


class Platform(str, Enum):
    """Supported social media platforms."""
    TWITTER = "twitter"
    INSTAGRAM = "instagram"
    LINKEDIN = "linkedin"


class TrendSource(str, Enum):
    """Sources for trend discovery."""
    GOOGLE_TRENDS = "google_trends"
    REDDIT = "reddit"
    HACKERNEWS = "hackernews"
    NEWS = "news"


class SocialMediaSettings(BaseSettings):
    """Main configuration for the social media automation system."""
    
    # Core Settings
    platforms: List[Platform] = Field(default=[Platform.TWITTER, Platform.LINKEDIN])
    keywords: List[str] = Field(default=["AI", "technology", "innovation"])
    brand_name: str = Field(default="AgnoAI")
    
    # Trend Scanner Settings
    trend_sources: List[TrendSource] = Field(default=[TrendSource.NEWS, TrendSource.REDDIT])
    trend_scan_interval_hours: int = Field(default=4, ge=1, le=24)
    max_trends_per_scan: int = Field(default=10, ge=1, le=50)
    trend_popularity_threshold: float = Field(default=0.3, ge=0.0, le=1.0)
    
    # Content Creator Settings  
    content_generation_model: str = Field(default="deepseek/deepseek-chat-v3.1:free")
    max_content_length: Dict[Platform, int] = Field(default={
        Platform.TWITTER: 280,
        Platform.LINKEDIN: 3000,
        Platform.INSTAGRAM: 2200
    })
    generate_images: bool = Field(default=True)
    image_generation_model: str = Field(default="dall-e-3")
    
    # Scheduler Settings
    timezone: str = Field(default="UTC")
    posting_schedule: Dict[Platform, Dict[str, Any]] = Field(default={
        Platform.TWITTER: {
            "optimal_hours": [9, 12, 15, 18],
            "max_posts_per_day": 5,
            "min_hours_between_posts": 2
        },
        Platform.LINKEDIN: {
            "optimal_hours": [8, 12, 17],
            "max_posts_per_day": 2,
            "min_hours_between_posts": 4
        },
        Platform.INSTAGRAM: {
            "optimal_hours": [11, 13, 17, 19],
            "max_posts_per_day": 3,
            "min_hours_between_posts": 3
        }
    })
    
    # Content Safety & Approval
    enable_content_filtering: bool = Field(default=True)
    require_human_approval: bool = Field(default=False)
    dry_run_mode: bool = Field(default=True)
    
    # File Paths
    content_output_dir: str = Field(default="./generated_content")
    trend_reports_dir: str = Field(default="./trend_reports")
    scheduled_posts_dir: str = Field(default="./scheduled_posts")
    logs_dir: str = Field(default="./logs")
    
    # API Keys (loaded from environment)
    openai_api_key: Optional[str] = Field(default=None)
    twitter_api_key: Optional[str] = Field(default=None)
    twitter_api_secret: Optional[str] = Field(default=None)
    twitter_access_token: Optional[str] = Field(default=None)
    twitter_access_secret: Optional[str] = Field(default=None)
    linkedin_access_token: Optional[str] = Field(default=None)
    instagram_username: Optional[str] = Field(default=None)
    instagram_password: Optional[str] = Field(default=None)
    
    class Config:
        env_file = ".env"
        env_prefix = "SOCIAL_MEDIA_"
        case_sensitive = False
    
    @validator("platforms", pre=True)
    def validate_platforms(cls, v):
        """Ensure all platforms are valid."""
        if isinstance(v, str):
            v = [v]
        return [Platform(p) if isinstance(p, str) else p for p in v]
    
    @validator("trend_sources", pre=True) 
    def validate_trend_sources(cls, v):
        """Ensure all trend sources are valid."""
        if isinstance(v, str):
            v = [v]
        return [TrendSource(s) if isinstance(s, str) else s for s in v]
    
    def get_platform_config(self, platform: Platform) -> Dict[str, Any]:
        """Get configuration for a specific platform."""
        config = {
            "max_length": self.max_content_length.get(platform, 1000),
            "schedule": self.posting_schedule.get(platform, {}),
        }
        
        # Add platform-specific API credentials
        if platform == Platform.TWITTER:
            config.update({
                "api_key": self.twitter_api_key,
                "api_secret": self.twitter_api_secret,
                "access_token": self.twitter_access_token,
                "access_secret": self.twitter_access_secret
            })
        elif platform == Platform.LINKEDIN:
            config.update({
                "access_token": self.linkedin_access_token
            })
        elif platform == Platform.INSTAGRAM:
            config.update({
                "username": self.instagram_username,
                "password": self.instagram_password
            })
        
        return config
    
    def create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.content_output_dir,
            self.trend_reports_dir,
            self.scheduled_posts_dir,
            self.logs_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)


# Brand guidelines template
BRAND_GUIDELINES = {
    "voice": {
        "tone": "professional yet approachable",
        "personality": ["innovative", "knowledgeable", "helpful"],
        "avoid": ["overly technical jargon", "aggressive language", "controversy"]
    },
    "content_themes": [
        "artificial intelligence",
        "software development", 
        "technology trends",
        "developer tools",
        "innovation"
    ],
    "hashtags": {
        Platform.TWITTER: ["#AI", "#TechTrends", "#Innovation", "#DevTools"],
        Platform.LINKEDIN: ["#ArtificialIntelligence", "#Technology", "#Innovation", "#SoftwareDevelopment"],
        Platform.INSTAGRAM: ["#AI", "#Tech", "#Innovation", "#Future", "#Development"]
    },
    "content_requirements": [
        "Always provide value to the audience",
        "Include relevant hashtags but don't overuse them",
        "Keep language accessible to a broad audience",
        "Include call-to-action when appropriate"
    ],
    "prohibited_content": [
        "political statements",
        "controversial opinions",
        "personal attacks",
        "unverified claims",
        "spam-like content"
    ]
}
