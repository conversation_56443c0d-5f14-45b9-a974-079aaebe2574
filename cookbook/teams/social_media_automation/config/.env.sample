# Social Media Automation Configuration
# Copy this file to .env and fill in your actual API keys and settings

# OpenAI Configuration (Required for content generation)
SOCIAL_MEDIA_OPENAI_API_KEY=your_openai_api_key_here

# Twitter/X API Configuration
SOCIAL_MEDIA_TWITTER_API_KEY=your_twitter_api_key
SOCIAL_MEDIA_TWITTER_API_SECRET=your_twitter_api_secret
SOCIAL_MEDIA_TWITTER_ACCESS_TOKEN=your_twitter_access_token
SOCIAL_MEDIA_TWITTER_ACCESS_SECRET=your_twitter_access_secret

# LinkedIn API Configuration
SOCIAL_MEDIA_LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token

# Instagram Configuration
SOCIAL_MEDIA_INSTAGRAM_USERNAME=your_instagram_username
SOCIAL_MEDIA_INSTAGRAM_PASSWORD=your_instagram_password

# Core Settings
SOCIAL_MEDIA_PLATFORMS=twitter,linkedin
SOCIAL_MEDIA_KEYWORDS=AI,technology,innovation,artificial intelligence
SOCIAL_MEDIA_BRAND_NAME=AgnoAI

# Trend Scanner Settings
SOCIAL_MEDIA_TREND_SOURCES=news,reddit
SOCIAL_MEDIA_TREND_SCAN_INTERVAL_HOURS=4
SOCIAL_MEDIA_MAX_TRENDS_PER_SCAN=10

# Content Settings
SOCIAL_MEDIA_GENERATE_IMAGES=true
SOCIAL_MEDIA_CONTENT_GENERATION_MODEL=gpt-4o
SOCIAL_MEDIA_IMAGE_GENERATION_MODEL=dall-e-3

# Safety & Approval Settings
SOCIAL_MEDIA_ENABLE_CONTENT_FILTERING=true
SOCIAL_MEDIA_REQUIRE_HUMAN_APPROVAL=false
SOCIAL_MEDIA_DRY_RUN_MODE=true

# Scheduling
SOCIAL_MEDIA_TIMEZONE=UTC

# File Paths (relative to project root)
SOCIAL_MEDIA_CONTENT_OUTPUT_DIR=./generated_content
SOCIAL_MEDIA_TREND_REPORTS_DIR=./trend_reports
SOCIAL_MEDIA_SCHEDULED_POSTS_DIR=./scheduled_posts
SOCIAL_MEDIA_LOGS_DIR=./logs
