"""
Social Media Automation Team for Agno

This module provides a complete social media automation pipeline using specialized agents:
- TrendScannerAgent: Monitors trending topics and news
- ContentCreatorAgent: Generates platform-specific content
- SchedulerAgent: Schedules and publishes content at optimal times
- SocialMediaTeam: Coordinates all agents in a unified workflow

The system is designed to be:
- Modular: Each agent has a focused responsibility
- Configurable: Supports multiple platforms and customization
- Safe: Includes dry-run mode and human approval workflows
- Robust: Built-in error handling and retry mechanisms
"""

from .agents.trend_scanner import TrendScannerAgent
from .agents.content_creator import ContentCreatorAgent
from .agents.scheduler import SchedulerAgent
from .coordinator import SocialMediaTeam

__all__ = [
    "TrendScannerAgent",
    "ContentCreatorAgent", 
    "SchedulerAgent",
    "SocialMediaTeam"
]

__version__ = "1.0.0"
